/**
 * 翻译相关的Action函数
 */

import type { BaseAction } from './index'
import { MessageType } from '../const';
import { getCurrentTab } from '@src/sidepanel/utils';
import { getCurrentEnv } from '../utils';
import { handleInsertDom } from '@src/contents/scripts/injectTranslate';

/**
 * 处理翻译API返回值的action函数
 * 这个函数封装了原本在批量翻译处理中的响应处理逻辑
 */
export const createInsertDomAction = (): BaseAction => {
  return {
    name: 'insertDom',
    description: '处理翻译API返回值，更新页面翻译图标',
    handler: async (response: any, group: any[]) => {
      console.log('handler', response);
      if (response.endFlag) {
        // 处理 response.list 数据结构
        if (response.list && Array.isArray(response.list)) {
          // 提取并解析每个对象的 content.text 字段（JSON字符串格式的数组）
          const results: string[] = [];

          for (const item of response.list) {
            if (item.content && item.content.text) {
              try {
                // 解析JSON字符串为数组
                const textArray = JSON.parse(item.content.text);
                if (Array.isArray(textArray)) {
                  results.push(...textArray);
                } else {
                  // 如果不是数组，直接添加
                  results.push(item.content.text);
                }
              } catch (error) {
                console.warn('解析content.text JSON失败，使用原始值:', item.content.text, error);
                // 解析失败时，直接使用原始值
                results.push(item.content.text);
              }
            }
          }

          console.log('提取的翻译结果数组:', results, getCurrentEnv());

          if (results.length > 0) {
            //侧栏环境中
            // if (getCurrentEnv() === 'sidepanel') {
            //   const currentTab = await getCurrentTab()
            //   chrome.tabs.sendMessage(currentTab.id,
            //     {
            //       type: MessageType.INSERT_DOM,
            //       data: results,
            //     }
            //   )
            //   return
            // }
            handleInsertDom(results)
          } else {
            // 处理错误情况
            console.error('翻译失败: 未能从响应中提取到有效的翻译结果', response);
          }
        } else if (response.result) {
          // 兼容旧格式：如果没有 list 字段但有 result 字段，使用原来的分割方式
          const results = response.result.split('###');
          console.log('使用旧格式分割的翻译结果:', results, getCurrentEnv());
          handleInsertDom(results)
        } else {
          // 处理错误情况
          console.error('翻译失败: 响应格式不正确或缺少必要数据', response?.error || response?.result || '未知错误');
        }
      }


    }
  };
};

/**
 * 获取所有翻译相关的actions
 */
export const getTranslationActions = (): BaseAction[] => {
  return [
    createInsertDomAction()
  ];
};