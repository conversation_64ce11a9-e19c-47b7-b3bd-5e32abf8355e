/**
 * 翻译相关的Action函数
 */

import type { BaseAction } from './index'
import { MessageType } from '../const';
import { getCurrentTab } from '@src/sidepanel/utils';
import { getCurrentEnv } from '../utils';
import { handleInsertDom } from '@src/contents/scripts/injectTranslate';

/**
 * 处理翻译API返回值的action函数
 * 这个函数封装了原本在批量翻译处理中的响应处理逻辑
 */
export const createInsertDomAction = (): BaseAction => {
  return {
    name: 'insertDom',
    description: '处理翻译API返回值，更新页面翻译图标',
    handler: async (response: any, group: any[]) => {
      console.log('handler', response);
      if (response.endFlag) {
        // 处理 response.list 数据结构
        if (response.list && Array.isArray(response.list)) {
          // 直接提取每个对象的 content.text 字段数组
          const results = response.list
            .filter((item: any) => item.content && item.content.text)
            .map((item: any) => item.content.text)
            .flat(); // 展平数组，以防 content.text 本身是嵌套数组

          console.log('提取的翻译结果数组:', results, getCurrentEnv());

          if (results.length > 0) {
            //侧栏环境中
            // if (getCurrentEnv() === 'sidepanel') {
            //   const currentTab = await getCurrentTab()
            //   chrome.tabs.sendMessage(currentTab.id,
            //     {
            //       type: MessageType.INSERT_DOM,
            //       data: results,
            //     }
            //   )
            //   return
            // }
            handleInsertDom(results)
          } else {
            // 处理错误情况
            console.error('翻译失败: 未能从响应中提取到有效的翻译结果', response);
          }
        } else if (response.result) {
          // 兼容旧格式：如果没有 list 字段但有 result 字段，使用原来的分割方式
          const results = response.result.split('###');
          console.log('使用旧格式分割的翻译结果:', results, getCurrentEnv());
          handleInsertDom(results)
        } else {
          // 处理错误情况
          console.error('翻译失败: 响应格式不正确或缺少必要数据', response?.error || response?.result || '未知错误');
        }
      }


    }
  };
};

/**
 * 获取所有翻译相关的actions
 */
export const getTranslationActions = (): BaseAction[] => {
  return [
    createInsertDomAction()
  ];
};